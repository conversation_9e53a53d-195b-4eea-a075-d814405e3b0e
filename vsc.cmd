use ```dotnet build "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\Lean\QuantConnect.Lean.sln" --configuration Debug ``` to check any compiling errors

use ```cd "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\Lean\Launcher\bin\Debug; .\QuantConnect.Lean.Launcher.exe --config D:\work\xstarwalker168\Python\Finance\QuantConnectLean\trading-bot-config.json --parameters tradeMode:2,symbol:SUI,currency:USDT,leverage:5 --results-destination-folder "D:/work/xstarwalker168/Python/Finance/QuantConnectLean/QC-Log-Dir" --algorithm-language CSharp --environment live-mexc --algorithm-location cCryptoBot.dll --data-folder "D:/work/xstarwalker168/Python/Finance/QuantConnectLean/Data"
``` to check runtime results
